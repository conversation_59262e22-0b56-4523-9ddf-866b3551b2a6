/* AdminLayout Component Styles */
.AdminLayout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-gray);
  position: relative;
}

.AdminLayout__container {
  display: flex;
  flex: 1;
  position: relative;
  padding-top: 70px; /* Account for fixed navbar height */
}

/* Sidebar */
.AdminLayout__sidebar {
  width: 280px;
  background-color: var(--white);
  border-right: 1px solid var(--light-gray);
  position: fixed;
  top: 70px; /* Position below fixed navbar */
  left: 0;
  height: calc(100vh - 70px); /* Full height minus navbar */
  z-index: 200;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow-y: auto;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  transform: translateX(0);
}

.AdminLayout__sidebar.collapsed {
  width: 80px;
}

.AdminLayout__sidebar::-webkit-scrollbar {
  width: 4px;
}

.AdminLayout__sidebar::-webkit-scrollbar-track {
  background: var(--bg-gray);
}

.AdminLayout__sidebar::-webkit-scrollbar-thumb {
  background: var(--light-gray);
  border-radius: 2px;
}

.AdminLayout__sidebar::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}

/* Main Content */
.AdminLayout__main {
  flex: 1;
  margin-left: 280px;
  padding: 35px var(--heading6) var(--heading6) var(--heading6) ;
  transition: margin-left 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  min-height: calc(100vh - 70px);
  overflow-y: auto;
  position: relative;
}

.AdminLayout__main.sidebar-collapsed {
  margin-left: 80px;
}

/* Breadcrumb Navigation */
.AdminLayout__breadcrumb {
  margin-bottom: var(--basefont);
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.breadcrumb-item {
  color: var(--dark-gray);
  transition: color 0.3s ease;
}

.breadcrumb-item:last-child {
  color: var(--btn-color);
  font-weight: 600;
}

.breadcrumb-separator {
  margin: 0 var(--smallfont);
  color: var(--light-gray);
}

/* Page Header */
.AdminLayout__header {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: var(--heading6);
  margin-bottom: var(--heading6);
}

.AdminLayout__title {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin: 0;
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
}

.AdminLayout__title-icon {
  font-size: var(--heading3);
  color: var(--btn-color);
}

/* Page Content */
.AdminLayout__content {
  
  padding: var(--heading6);
 
}

/* Tablet and Mobile Responsive */
@media (max-width: 1024px) {
  .AdminLayout__container {
    padding-top: 65px; /* Slightly smaller navbar on tablet */
  }

  .AdminLayout__sidebar {
    top: 65px;
    height: calc(100vh - 65px);
  }
}

/* Tablet Responsive Design */
@media (max-width: 1024px) and (min-width: 769px) {
  .AdminLayout__sidebar {
    width: 240px;
  }

  .AdminLayout__main {
    margin-left: 240px;
    padding: var(--heading6) var(--basefont);
  }

  .AdminLayout__main.sidebar-collapsed {
    margin-left: 70px;
  }

  .AdminLayout__sidebar.collapsed {
    width: 70px;
  }

  .AdminLayout__breadcrumb {
    margin-bottom: var(--smallfont);
  }

  .breadcrumb-nav {
    font-size: var(--smallfont);
  }
}

/* Mobile Sidebar */
@media (max-width: 768px) {
  .AdminLayout__container {
    padding-top: 60px; /* Mobile navbar height */
  }

  .AdminLayout__sidebar {
    width: 280px;
    left: -280px; /* Hidden by default */
    top: 60px;
    height: calc(100vh - 60px);
    z-index: 300; /* Higher z-index for mobile overlay */
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
    transition: left 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.4s ease;
    transform: translateX(-100%);
    will-change: transform, left;
  }

  .AdminLayout__sidebar.mobile-open {
    left: 0;
    transform: translateX(0);
    box-shadow: 8px 0 24px rgba(0, 0, 0, 0.2);
  }

  .AdminLayout__sidebar.collapsed {
    width: 280px; /* Full width on mobile even when "collapsed" */
    left: -280px;
  }

  .AdminLayout__main {
    margin-left: 0;
    padding: var(--basefont);
    min-height: calc(100vh - 60px);
  }

  .AdminLayout__main.sidebar-collapsed {
    margin-left: 0;
  }

  .AdminLayout__overlay {
    position: fixed;
    top: 60px; /* Start below navbar */
    left: 0;
    width: 100%;
    height: calc(100vh - 60px);
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 250; /* Between sidebar and content */
    backdrop-filter: blur(2px);
    opacity: 1;
    visibility: visible;
    transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                visibility 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                backdrop-filter 0.4s ease;
  }

  .AdminLayout__header {
    padding: var(--basefont);
  }

  .AdminLayout__title {
    font-size: var(--heading5);
  }

  .AdminLayout__title-icon {
    font-size: var(--heading4);
  }

  .AdminLayout__content {
    padding: var(--basefont);
  }
}

@media (max-width: 480px) {
  .AdminLayout__container {
    padding-top: 55px; /* Even smaller navbar on small mobile */
  }

  .AdminLayout__sidebar {
    top: 55px;
    height: calc(100vh - 55px);
    width: 100vw; /* Full width on very small screens */
    left: -100vw;
    transform: translateX(-100%);
    transition: left 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
                box-shadow 0.4s ease;
  }

  .AdminLayout__sidebar.mobile-open {
    left: 0;
    transform: translateX(0);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  }

  .AdminLayout__overlay {
    top: 55px;
    height: calc(100vh - 55px);
  }

  .AdminLayout__main {
    padding: var(--smallfont);
    min-height: calc(100vh - 55px);
  }

  .AdminLayout__breadcrumb {
    margin-bottom: var(--smallfont);
  }

  .breadcrumb-nav {
    font-size: var(--extrasmallfont);
  }

  .AdminLayout__header {
    padding: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .AdminLayout__title {
    font-size: var(--heading6);
  }

  .AdminLayout__title-icon {
    font-size: var(--heading5);
  }

  .AdminLayout__content {
    padding: var(--smallfont);
  }
}

/* Smooth transitions */
.AdminLayout__sidebar,
.AdminLayout__main {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Focus states for accessibility */
.breadcrumb-item:focus {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
  border-radius: var(--border-radius);
}

/* Loading states */
.AdminLayout__content.loading {
  position: relative;
  overflow: hidden;
}

.AdminLayout__content.loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* Error states */
.AdminLayout__content.error {
  border: 1px solid #ff6b6b;
  background-color: #fff5f5;
}

/* Success states */
.AdminLayout__content.success {
  border: 1px solid #51cf66;
  background-color: #f3fff3;
}

/* Custom scrollbar for main content */
.AdminLayout__content::-webkit-scrollbar {
  width: 6px;
}

.AdminLayout__content::-webkit-scrollbar-track {
  background: var(--bg-gray);
  border-radius: 3px;
}

.AdminLayout__content::-webkit-scrollbar-thumb {
  background: var(--light-gray);
  border-radius: 3px;
}

.AdminLayout__content::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}
